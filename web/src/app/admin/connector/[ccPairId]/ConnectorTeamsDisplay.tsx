"use client";

import React, { useState } from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { FiEdit2, FiUsers, FiGlobe } from "react-icons/fi";
import { useUserTeams } from "@/lib/hooks";
import { UserRole } from "@/lib/types";
import { useUser } from "@/components/user/UserProvider";
import { ConnectorTeamsEditor } from "./ConnectorTeamsEditor";
import { errorHandlingFetcher } from "@/lib/fetcher";
import useSWR from "swr";

interface ConnectorTeamsDisplayProps {
  ccPairId: number;
  isPublic: boolean;
  isEditable: boolean;
}

export function ConnectorTeamsDisplay({
  ccPairId,
  isPublic,
  isEditable,
}: ConnectorTeamsDisplayProps) {
  const { user } = useUser();
  const { data: userTeams } = useUserTeams();
  const [showEditor, setShowEditor] = useState(false);

  // Fetch current team associations
  const { data: currentTeams, error } = useSWR<number[]>(
    `/api/manage/admin/cc-pair/${ccPairId}/teams`,
    errorHandlingFetcher
  );

  // Only show for admin users
  if (!user || user.role !== UserRole.ADMIN) {
    return null;
  }

  if (error) {
    return (
      <div className="text-sm text-error">
        Failed to load team associations
      </div>
    );
  }

  if (!currentTeams) {
    return (
      <div className="text-sm text-text-500">
        Loading team associations...
      </div>
    );
  }

  // Get team names for display
  const teamNames = currentTeams
    .map((teamId) => userTeams?.find((team) => team.id === teamId)?.name)
    .filter(Boolean);

  return (
    <>
      <div className="space-y-3">
        <div className="flex items-center justify-between">
          <h4 className="text-sm font-medium flex items-center gap-2">
            <FiUsers className="h-4 w-4" />
            Access Control
          </h4>
          {isEditable && (
            <Button
              variant="ghost"
              size="sm"
              onClick={() => setShowEditor(true)}
              className="h-8 px-2"
            >
              <FiEdit2 className="h-3 w-3 mr-1" />
              Edit
            </Button>
          )}
        </div>

        <div className="space-y-2">
          <div className="flex items-center gap-2">
            <span className="text-sm text-text-600">Privacy:</span>
            <Badge variant={isPublic ? "default" : "secondary"}>
              {isPublic ? (
                <>
                  <FiGlobe className="h-3 w-3 mr-1" />
                  Public
                </>
              ) : (
                <>
                  <FiUsers className="h-3 w-3 mr-1" />
                  Private
                </>
              )}
            </Badge>
          </div>

          {!isPublic && teamNames.length > 0 && (
            <div className="space-y-1">
              <span className="text-sm text-text-600">Assigned Teams:</span>
              <div className="flex flex-wrap gap-1">
                {teamNames.map((teamName, index) => (
                  <Badge key={index} variant="outline" className="text-xs">
                    {teamName}
                  </Badge>
                ))}
              </div>
            </div>
          )}

          {!isPublic && teamNames.length === 0 && (
            <div className="text-sm text-text-500 italic">
              No teams assigned
            </div>
          )}
        </div>
      </div>

      {showEditor && (
        <ConnectorTeamsEditor
          ccPairId={ccPairId}
          currentTeams={currentTeams}
          isPublic={isPublic}
          onClose={() => setShowEditor(false)}
        />
      )}
    </>
  );
}
