"use client";

import React, { useState, useEffect } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { PrivacyToggle } from "@/components/PrivacyToggle";
import { usePopup } from "@/components/admin/connectors/Popup";
import { useUserTeams } from "@/lib/hooks";
import { UserRole } from "@/lib/types";
import { useUser } from "@/components/user/UserProvider";
import { Formik, FormikProps } from "formik";
import { mutate } from "swr";
import { buildCCPairInfoUrl } from "./lib";

interface ConnectorTeamsEditorProps {
  ccPairId: number;
  currentTeams: number[];
  isPublic: boolean;
  onClose: () => void;
}

interface FormValues {
  is_public: boolean;
  selectedTeams: number[];
}

export function ConnectorTeamsEditor({
  ccPairId,
  currentTeams,
  isPublic,
  onClose,
}: ConnectorTeamsEditorProps) {
  const { user } = useUser();
  const { data: userTeams } = useUserTeams();
  const { popup, setPopup } = usePopup();
  const [isSubmitting, setIsSubmitting] = useState(false);

  // Only show for admin users
  if (!user || user.role !== UserRole.ADMIN) {
    return null;
  }

  const initialValues: FormValues = {
    is_public: isPublic,
    selectedTeams: currentTeams,
  };

  const handleSubmit = async (values: FormValues) => {
    setIsSubmitting(true);
    try {
      // Update team associations
      const response = await fetch(`/api/manage/admin/cc-pair/${ccPairId}/teams`, {
        method: "PUT",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify(values.is_public ? [] : values.selectedTeams),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.detail || "Failed to update team associations");
      }

      // Refresh the connector data
      await mutate(buildCCPairInfoUrl(ccPairId));

      setPopup({
        message: "Team associations updated successfully",
        type: "success",
      });

      onClose();
    } catch (error) {
      setPopup({
        message: `Failed to update team associations: ${error}`,
        type: "error",
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      {popup}
      <div className="bg-background border border-border rounded-lg p-6 max-w-md w-full mx-4">
        <h3 className="text-lg font-semibold mb-4">Edit Connector Access</h3>
        
        <Formik
          initialValues={initialValues}
          onSubmit={handleSubmit}
          enableReinitialize
        >
          {(formikProps: FormikProps<FormValues>) => (
            <form onSubmit={formikProps.handleSubmit}>
              <div className="mb-6">
                <PrivacyToggle
                  formikProps={formikProps}
                  userTeams={userTeams || []}
                  objectName="connector"
                  setPopup={setPopup}
                />
              </div>

              <div className="flex justify-end space-x-3">
                <Button
                  type="button"
                  variant="outline"
                  onClick={onClose}
                  disabled={isSubmitting}
                >
                  Cancel
                </Button>
                <Button
                  type="submit"
                  disabled={isSubmitting}
                >
                  {isSubmitting ? "Updating..." : "Update"}
                </Button>
              </div>
            </form>
          )}
        </Formik>
      </div>
    </div>
  );
}
